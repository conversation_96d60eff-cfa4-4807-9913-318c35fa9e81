import json

# Define currency unit multipliers
unit_multipliers = {
    "thousand": 1e3,
    "thousands": 1e3,
    "lakh": 1e5,
    "lakhs": 1e5,
    "crore": 1e7,
    "crores": 1e7,
    "million": 1e6,
    "millions": 1e6,
    "billion": 1e9,
    "billions": 1e9,
    "trillion": 1e12,
    "trillions": 1e12,
    "arab": 1e9,
    "arabs": 1e9
}

def get_multiplier(currency_unit):
    if not currency_unit or not isinstance(currency_unit, str):
        return None
    return unit_multipliers.get(currency_unit.strip().lower())

def convert_value(val, multiplier):
    if isinstance(val, (int, float)):
        return int(val * multiplier)
    return val

def convert_assets(assets):
    multiplier = get_multiplier(assets.get("currency_unit"))
    if not multiplier:
        return assets
    for section in ["current_assets", "other_non_current_assets", "property_plant_equipment"]:
        asset_list = assets.get(section, {}).get("asset_type", [])
        for item in asset_list:
            if "value" in item and isinstance(item["value"], (int, float)):
                item["value"] = convert_value(item["value"], multiplier)
    return assets

def convert_cashflows(cashflows):
    if not isinstance(cashflows, list) or not cashflows:
        return cashflows
    multiplier = get_multiplier(cashflows[-1].get("currency_unit"))
    if not multiplier:
        return cashflows
    for entry in cashflows[:-1]:
        for field in ["capex", "cash_from_operations", "free_cash_flow"]:
            if field in entry and isinstance(entry[field], (int, float)):
                entry[field] = convert_value(entry[field], multiplier)
    return cashflows

def convert_revenue_expenses(revenue_expenses):
    if not isinstance(revenue_expenses, list) or not revenue_expenses:
        return revenue_expenses
    multiplier = get_multiplier(revenue_expenses[-1].get("currency_unit"))
    if not multiplier:
        return revenue_expenses
    for entry in revenue_expenses[:-1]:
        expenses = entry.get("expenses", {})
        revenue = entry.get("revenue", {})
        for field in ["corporate_tax", "depreciation", "exceptions_before_tax", "fuel_expenses", "interest", "o_m_expenses"]:
            if field in expenses and isinstance(expenses[field], (int, float)):
                expenses[field] = convert_value(expenses[field], multiplier)
        for field in ["other_income", "revenue_operations"]:
            if field in revenue and isinstance(revenue[field], (int, float)):
                revenue[field] = convert_value(revenue[field], multiplier)
    return revenue_expenses

def convert_debt_equity_analysis(dea):
    if not isinstance(dea, list) or not dea:
        return dea
    multiplier = get_multiplier(dea[-1].get("currency_unit"))
    if not multiplier:
        return dea
    for entry in dea[:-1]:
        for key in ["equity", "long_term", "short_term"]:
            val = entry.get(key, {}).get("value")
            if isinstance(val, (int, float)):
                entry[key]["value"] = convert_value(val, multiplier)
    return dea

def convert_equity_liability(equity_liability):
    multiplier = get_multiplier(equity_liability.get("currency_unit"))
    if not multiplier:
        return equity_liability

    # Convert equity share capital
    esc_list = equity_liability.get("equity", {}).get("equity_share_capital", [])
    for item in esc_list:
        if "value" in item and isinstance(item["value"], (int, float)):
            item["value"] = convert_value(item["value"], multiplier)
        if "no_of_shares" in item and isinstance(item["no_of_shares"], (int, float)):
            item["no_of_shares"] = convert_value(item["no_of_shares"], multiplier)

    # Convert other equity
    oe_list = equity_liability.get("equity", {}).get("other_equity", [])
    for item in oe_list:
        if "value" in item and isinstance(item["value"], (int, float)):
            item["value"] = convert_value(item["value"], multiplier)

    # Convert debts
    for debt_type in ["long_term_debt", "short_term_debt"]:
        for sub_type in ["junior_debt", "senior_debt"]:
            val = equity_liability.get(debt_type, {}).get(sub_type, {}).get("value")
            if isinstance(val, (int, float)):
                equity_liability[debt_type][sub_type]["value"] = convert_value(val, multiplier)

    # Convert others
    others_list = equity_liability.get("others", {}).get("others", [])
    for item in others_list:
        if "value" in item and isinstance(item["value"], (int, float)):
            item["value"] = convert_value(item["value"], multiplier)

    return equity_liability

def convert_currency_units(financial_data: dict) -> dict:
    if "assets" in financial_data:
        financial_data["assets"] = convert_assets(financial_data["assets"])
    if "cashflows" in financial_data:
        financial_data["cashflows"] = convert_cashflows(financial_data["cashflows"])
    if "revenue_expenses" in financial_data:
        financial_data["revenue_expenses"] = convert_revenue_expenses(financial_data["revenue_expenses"])
    if "debt_equity_analysis" in financial_data:
        financial_data["debt_equity_analysis"] = convert_debt_equity_analysis(financial_data["debt_equity_analysis"])
    if "equity_liability" in financial_data:
        financial_data["equity_liability"] = convert_equity_liability(financial_data["equity_liability"])
    return financial_data


def remove_currency_unit_keys(financial_data: dict) -> dict:
    # Remove from 'assets'
    if "assets" in financial_data and "currency_unit" in financial_data["assets"]:
        del financial_data["assets"]["currency_unit"]

    # Remove from 'cashflows' if last item has only 'currency_unit'
    if "cashflows" in financial_data and isinstance(financial_data["cashflows"], list):
        last_item = financial_data["cashflows"][-1]
        if isinstance(last_item, dict) and "currency_unit" in last_item and len(last_item) == 1:
            financial_data["cashflows"].pop()

    # Remove from 'revenue_expenses' if last item has only 'currency_unit'
    if "revenue_expenses" in financial_data and isinstance(financial_data["revenue_expenses"], list):
        last_item = financial_data["revenue_expenses"][-1]
        if isinstance(last_item, dict) and "currency_unit" in last_item and len(last_item) == 1:
            financial_data["revenue_expenses"].pop()

    # Remove from 'debt_equity_analysis' if last item has only 'currency_unit'
    if "debt_equity_analysis" in financial_data and isinstance(financial_data["debt_equity_analysis"], list):
        last_item = financial_data["debt_equity_analysis"][-1]
        if isinstance(last_item, dict) and "currency_unit" in last_item and len(last_item) == 1:
            financial_data["debt_equity_analysis"].pop()

    # Remove from 'equity_liability'
    if "equity_liability" in financial_data and "currency_unit" in financial_data["equity_liability"]:
        del financial_data["equity_liability"]["currency_unit"]

    return financial_data

