import os
import json
from glob import glob

import os
import json
from glob import glob
from datetime import datetime
from currency_handler import convert_currency_units,remove_currency_unit_keys
def extract_year_from_assets_date(data):
    try:
        date_str = data.get("assets", {}).get("date", "")
        return int(datetime.strptime(date_str, "%d/%m/%Y").year)
    except Exception:
        return None

def merge_powerplant_jsons(powerplant_name: str):
    """
    Merges JSON files in the specified folder:
    - Uses the file with the latest `assets.date` year as base.
    - Ignores `assets` from all other files.
    - Merges `cashflows`, `revenue_expenses`, and `credit_rating` by year.
    - Writes merged result to `final.json`.
    """
    folder_path = os.path.join('outputs', powerplant_name)
    json_files = sorted(glob(os.path.join(folder_path, "consolidated_output*.json")))

    if not json_files:
        raise FileNotFoundError(f"No consolidated_output*.json files found in {folder_path}")

    # Step 1: Select base file with max year in assets.date
    base_file = None
    max_year = -1
    for file in json_files:
        try:
            with open(file, "r") as f:
                data = json.load(f)
                year = extract_year_from_assets_date(data)
                if year and year > max_year:
                    max_year = year
                    base_file = file
        except Exception as e:
            print(f"⚠️ Skipping {file}: {e}")

    if not base_file:
        raise ValueError("Couldn't determine base file from assets.date fields.")

    print(f"🟢 Using base file: {os.path.basename(base_file)} with year: {max_year}")

    with open(base_file, "r") as f:
        final_data = json.load(f)

    existing_cashflow_years = {entry["year"] for entry in final_data.get("cashflows", [])}
    existing_revenue_years = {entry["year"] for entry in final_data.get("revenue_expenses", [])}
    existing_credit_years = {}
    for rating in final_data.get("credit_rating", []):
        key = (rating.get("agency", ""), rating.get("name", ""))
        years = {r["year"] for r in rating.get("yearwise_rating", [])}
        existing_credit_years[key] = years

    # Step 2: Merge all other files
    for file in json_files:
        if file == base_file:
            continue
        try:
            with open(file, "r") as f:
                data = json.load(f)

            for entry in data.get("cashflows", []):
                if entry["year"] not in existing_cashflow_years:
                    final_data.setdefault("cashflows", []).append(entry)
                    existing_cashflow_years.add(entry["year"])

            for entry in data.get("revenue_expenses", []):
                if entry["year"] not in existing_revenue_years:
                    final_data.setdefault("revenue_expenses", []).append(entry)
                    existing_revenue_years.add(entry["year"])

            for rating in data.get("credit_rating", []):
                key = (rating.get("agency", ""), rating.get("name", ""))
                existing_years = existing_credit_years.get(key, set())

                new_ratings = [
                    r for r in rating.get("yearwise_rating", [])
                    if r["year"] not in existing_years
                ]

                if new_ratings:
                    found = False
                    for existing_rating in final_data.get("credit_rating", []):
                        if existing_rating.get("agency", "") == rating.get("agency", "") and \
                           existing_rating.get("name", "") == rating.get("name", ""):
                            existing_rating["yearwise_rating"].extend(new_ratings)
                            existing_credit_years[key].update(r["year"] for r in new_ratings)
                            found = True
                            break

                    if not found:
                        new_block = {
                            "agency": rating.get("agency", ""),
                            "name": rating.get("name", ""),
                            "yearwise_rating": new_ratings
                        }
                        final_data.setdefault("credit_rating", []).append(new_block)
                        existing_credit_years[key] = {r["year"] for r in new_ratings}

        except Exception as e:
            print(f"⚠️ Failed to merge {file}: {e}")

    # Save merged output
    output_file = os.path.join(folder_path, "final.json")
    with open(output_file, "w") as f:
        json.dump(final_data, f, indent=2)

    print(f"✅ 'final.json' has been created in '{folder_path}' with merged data.")


# Example usage:
# merge_powerplant_jsons("Eskom")


import json
import os
from collections import OrderedDict

def create_final_output_with_static(powerplant_name: str):
    """
    Creates final_output.json with static.json data appearing first (on top),
    followed by the contents of final.json.
    """

    folder_path = "outputs/" + powerplant_name
    final_json_path =  folder_path+"/"+"final.json"
    final_output_path = folder_path+ "/" + "final_output.json"
    static_file_path = os.path.join(os.getcwd(), "static.json") 

    # Sanity checks
    if not os.path.exists(final_json_path):
        raise FileNotFoundError(f"'final.json' not found in: {folder_path}")
    if not os.path.exists(static_file_path):
        raise FileNotFoundError(f"'static.json' not found in current directory: {os.getcwd()}")

    # Load JSONs
    with open(static_file_path, "r") as f:
        static_data = json.load(f, object_pairs_hook=OrderedDict)

    with open(final_json_path, "r") as f:
        final_data = json.load(f, object_pairs_hook=OrderedDict)

    # Combine with static data first
    combined_data = OrderedDict()
    combined_data.update(static_data)
    combined_data.update(final_data)

    # Write to final_output.json
    with open(final_output_path, "w") as f:
        json.dump(combined_data, f, indent=2)

    print(f"'final_output.json' created at: {final_output_path} (with static.json at the top)")



# create_final_output_with_static("Eskom")

def get_unique_output_path(base_folder: str, filename: str = "consolidated_output.json") -> str:
    os.makedirs(base_folder, exist_ok=True)
    
    base_name, ext = os.path.splitext(filename)
    counter = 0
    output_path = os.path.join(base_folder, filename)
    
    while os.path.exists(output_path):
        counter += 1
        output_path = os.path.join(base_folder, f"{base_name}{counter}{ext}")
    
    return output_path



import json
import shutil
from pathlib import Path
from typing import Union

def generate_final_output_package(final_output_json_path: Union[str, Path],
    financial_json_path: Union[str, Path],
    assumptions_json_path: Union[str, Path],
    plant_name: str,
    entity_id: str,
    static_data_path: Union[str, Path] = "static_data.json"):

    """
    Merges debt_equity_analysis and equity_liability into final_output.json,
    adds a top-level 'pk': <entity_id> field to each JSON,
    and saves the updated files to final_output/<plant_name>/
    """

    final_output_json_path = Path(final_output_json_path)
    if not final_output_json_path.exists():
        raise FileNotFoundError(f"❌ final_output.json not found at: {final_output_json_path}")

    with open(final_output_json_path, 'r', encoding='utf-8') as f:
        final_output_data = json.load(f)

    with open(financial_json_path, 'r', encoding='utf-8') as f:
        financial_data = json.load(f)

    # Merge financial data
    if "debt_equity_analysis" in financial_data:
        final_output_data["debt_equity_analysis"] = financial_data["debt_equity_analysis"]
    if "equity_liability" in financial_data:
        final_output_data["equity_liability"] = financial_data["equity_liability"]

    # Insert pk at the top level
    final_output_data = {"pk": entity_id, **final_output_data}

    # Output dir
    output_dir = Path("final_output") / plant_name.replace(" ", "_")
    output_dir.mkdir(parents=True, exist_ok=True)

    # Save financial details file
    final_output_file1 = output_dir / f"{plant_name.replace(' ', '_')}_financial_details1.json"
    with open(final_output_file1, 'w', encoding='utf-8') as f:
        json.dump(final_output_data, f, indent=2, ensure_ascii=False)

    with open(final_output_file1, "r") as f:
         financial_data = json.load(f)

    converted_data = convert_currency_units(financial_data)
    final_data = remove_currency_unit_keys(converted_data)
    final_output_file = output_dir / f"{plant_name.replace(' ', '_')}_financial_details.json"
    with open(final_output_file, "w") as f:
         json.dump(final_data, f, indent=2)
    # Copy and update assumptions JSON
    if assumptions_json_path and Path(assumptions_json_path).exists():
        with open(assumptions_json_path, 'r', encoding='utf-8') as f:
            assumptions_data = json.load(f)

        assumptions_data["pk"] = entity_id

        assumptions_file = output_dir / f"{plant_name.replace(' ', '_')}_financial_assumptions.json"
        with open(assumptions_file, 'w', encoding='utf-8') as f:
            json.dump(assumptions_data, f, indent=2, ensure_ascii=False)

    # Copy and update static_data.json
    static_data_file = Path(static_data_path)
    if static_data_file.exists():
        with open(static_data_file, 'r', encoding='utf-8') as f:
            static_data = json.load(f)

        static_data["pk"] = entity_id

        static_output_file = output_dir / "static_data.json"
        with open(static_output_file, 'w', encoding='utf-8') as f:
            json.dump(static_data, f, indent=2, ensure_ascii=False)
    else:
        print(f"⚠️ Warning: static_data.json not found at {static_data_file}")

    print(f"✅ Final output files saved to: {output_dir}")
    return str(final_output_file)





import os

def get_image_parent_folder(image_path):
    return os.path.dirname(image_path)



import shutil

def delete_folders_in_current_directory():
    folders_to_delete = ["final_output", "output_part2", "outputs"]
    current_dir = os.getcwd()

    for folder_name in folders_to_delete:
        folder_path = os.path.join(current_dir, folder_name)
        if os.path.isdir(folder_path):
            try:
                shutil.rmtree(folder_path)
                print(f"✅ Deleted: {folder_path}")
            except Exception as e:
                print(f"❌ Failed to delete {folder_path}: {e}")
        else:
            print(f"ℹ️ Folder not found: {folder_path}")

